"""
CTA策略分析系统主程序
整合各分析模块，生成综合分析报告
"""

import os
import sys
import json
import logging
import argparse
import pandas as pd
from datetime import datetime
from typing import Dict, Any

# 添加模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

# 导入各分析模块
from data_loader import DataLoader
from profit_loss_analysis import ProfitLossAnalyzer
from position_analysis import PositionAnalyzer
from returns_calculation import ReturnsCalculator
from performance_risk_analysis import PerformanceRiskAnalyzer
from contribution_analysis import ContributionAnalyzer
from report_generator import ReportGenerator
from daily_profit_loss_series import DailyProfitLossAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cta_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CTAAnalysisSystem:
    """CTA策略分析系统主类"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化分析系统
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = config_dir
        self.configs = {}
        self.analysis_results = {}
        
        # 加载配置文件
        self._load_configurations()
        
        # 初始化各分析模块
        self._initialize_analyzers()

    def _load_configurations(self):
        """加载所有配置文件"""
        try:
            config_files = {
                'analysis_config': 'analysis_config.json',
                'report_config': 'report_config.json',
                'custom_definitions': 'custom_definitions.json',
                'output_paths': 'output_paths.json'
            }

            for config_name, config_file in config_files.items():
                config_path = os.path.join(self.config_dir, config_file)

                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        self.configs[config_name] = json.load(f)
                    logger.info(f"成功加载配置文件: {config_file}")
                else:
                    logger.warning(f"配置文件不存在: {config_file}")
                    self.configs[config_name] = {}

            # 创建输出目录结构
            self._create_output_directories()

        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            raise

    def _create_output_directories(self):
        """创建输出目录结构"""
        try:
            output_config = self.configs.get('output_paths', {})
            output_root = output_config.get('output_root_dir', 'output')

            # 创建根目录
            os.makedirs(output_root, exist_ok=True)

            # 创建报告目录
            reports_config = output_config.get('reports', {})
            for report_type, report_path in reports_config.items():
                os.makedirs(report_path, exist_ok=True)

            # 创建图表目录
            plots_dir = output_config.get('plots', 'plots')
            os.makedirs(plots_dir, exist_ok=True)

            # 创建中间数据目录
            intermediate_config = output_config.get('intermediate_data', {})
            for data_type, data_path in intermediate_config.items():
                if data_type != 'root':
                    os.makedirs(data_path, exist_ok=True)

            # 创建收益率数据目录
            returns_dir = output_config.get('returns_data', 'returns_data')
            os.makedirs(returns_dir, exist_ok=True)

            logger.info("输出目录结构创建完成")

        except Exception as e:
            logger.error(f"创建输出目录失败: {str(e)}")
            raise
    
    def _initialize_analyzers(self):
        """初始化各分析模块"""
        try:
            # 数据加载器
            self.data_loader = DataLoader("input")
            
            # 各分析器
            self.pnl_analyzer = ProfitLossAnalyzer(self.configs['analysis_config'])
            self.position_analyzer = PositionAnalyzer(self.configs['analysis_config'])
            self.returns_calculator = ReturnsCalculator(self.configs['custom_definitions'])
            self.performance_analyzer = PerformanceRiskAnalyzer(self.configs['custom_definitions'])
            self.contribution_analyzer = ContributionAnalyzer(self.configs['analysis_config'])
            self.report_generator = ReportGenerator(self.configs['report_config'])
            self.daily_pnl_analyzer = DailyProfitLossAnalyzer(self.configs['analysis_config'])
            
            logger.info("所有分析模块初始化完成")
            
        except Exception as e:
            logger.error(f"初始化分析模块失败: {str(e)}")
            raise
    
    def run_analysis(self, start_date: str = None, end_date: str = None, 
                    modules: list = None) -> Dict[str, Any]:
        """
        运行完整的CTA策略分析
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            modules: 要运行的模块列表，None表示运行所有模块
            
        Returns:
            分析结果字典
        """
        try:
            logger.info("开始CTA策略分析...")
            
            # 1. 加载数据
            logger.info("正在加载数据...")
            cta_data = self.data_loader.load_cta_data()
            position_data = self.data_loader.load_position_data()
            
            # 按日期过滤数据
            if start_date or end_date:
                cta_data, position_data = self.data_loader.filter_data_by_date(start_date, end_date)

            logger.info(f"数据加载完成，CTA数据: {len(cta_data)} 条，持仓数据: {len(position_data)} 条")

            # 转换日期参数
            start_date_obj = None
            end_date_obj = None
            if start_date:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
            if end_date:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')

            # 2. 执行各模块分析
            if modules is None:
                modules = ['profit_loss_analysis', 'position_analysis', 'returns_calculation',
                          'performance_risk_analysis', 'contribution_analysis']

            # 盈亏分析（期间汇总）
            if 'profit_loss_analysis' in modules:
                logger.info("正在执行盈亏分析...")
                pnl_results = self.pnl_analyzer.analyze_profit_loss(
                    cta_data, start_date_obj, end_date_obj
                )
                self.analysis_results['profit_loss_analysis'] = pnl_results

                # 生成每日序列数据
                logger.info("正在生成每日盈亏序列数据...")
                daily_series_data = self.daily_pnl_analyzer.generate_comprehensive_daily_pivot(
                    cta_data, start_date_obj, end_date_obj
                )

                # 保存每日序列数据到Excel
                self._save_daily_series_to_excel(daily_series_data)

            # 持仓分析
            if 'position_analysis' in modules:
                logger.info("正在执行持仓分析...")
                # 对于持仓分析，使用end_date作为分析日期，如果没有则使用最新日期
                analysis_date = end_date_obj if end_date_obj else None
                self.analysis_results['position_analysis'] = self.position_analyzer.analyze_positions(cta_data, analysis_date)
            
            # 收益率计算
            if 'returns_calculation' in modules:
                logger.info("正在执行收益率计算...")
                returns_results = self.returns_calculator.calculate_returns(cta_data, position_data)
                
                # 生成收益率汇总
                returns_summary = self.returns_calculator.generate_returns_summary(returns_results)
                returns_results['returns_summary'] = returns_summary
                
                self.analysis_results['returns_calculation'] = returns_results
            
            # 性能风险分析
            if 'performance_risk_analysis' in modules and 'returns_calculation' in self.analysis_results:
                logger.info("正在执行性能风险分析...")
                returns_results = self.analysis_results['returns_calculation']
                self.analysis_results['performance_risk_analysis'] = self.performance_analyzer.analyze_performance_risk(returns_results)
            
            # 贡献分析
            if 'contribution_analysis' in modules:
                logger.info("正在执行贡献分析...")
                self.analysis_results['contribution_analysis'] = self.contribution_analyzer.analyze_contributions(cta_data)
            
            logger.info("所有分析模块执行完成")
            return self.analysis_results
            
        except Exception as e:
            logger.error(f"分析执行失败: {str(e)}")
            raise
    
    def generate_reports(self, report_type: str = 'all') -> Dict[str, str]:
        """
        生成分析报告
        
        Args:
            report_type: 报告类型 ('today', 'this_year', 'custom_period', 'all')
            
        Returns:
            生成的报告文件路径字典
        """
        try:
            logger.info(f"正在生成 {report_type} 报告...")
            
            if not self.analysis_results:
                raise ValueError("请先运行分析")
            
            report_paths = self.report_generator.generate_reports(self.analysis_results, report_type)
            
            logger.info(f"报告生成完成，共生成 {len(report_paths)} 个文件")
            return report_paths
            
        except Exception as e:
            logger.error(f"报告生成失败: {str(e)}")
            raise
    
    def run_full_analysis(self, start_date: str = None, end_date: str = None, 
                         modules: list = None, report_type: str = 'all') -> Dict[str, Any]:
        """
        运行完整的分析流程（分析+报告生成）
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            modules: 要运行的模块列表
            report_type: 报告类型
            
        Returns:
            包含分析结果和报告路径的字典
        """
        try:
            # 执行分析
            analysis_results = self.run_analysis(start_date, end_date, modules)
            
            # 生成报告
            report_paths = self.generate_reports(report_type)
            
            return {
                'analysis_results': analysis_results,
                'report_paths': report_paths,
                'summary': {
                    'analysis_modules': len(analysis_results),
                    'report_files': len(report_paths),
                    'execution_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }
            
        except Exception as e:
            logger.error(f"完整分析流程失败: {str(e)}")
            raise

    def _save_daily_series_to_excel(self, daily_series_data: Dict[str, pd.DataFrame]):
        """
        保存每日序列数据到Excel文件

        Args:
            daily_series_data: 每日序列数据字典
        """
        try:
            if not daily_series_data:
                logger.warning("每日序列数据为空，跳过保存")
                return

            output_config = self.configs.get('output_paths', {})
            output_root = output_config.get('output_root_dir', 'output')
            filename = output_config.get('daily_profit_loss_excel', 'daily_profit_loss_series.xlsx')

            output_path = os.path.join(output_root, filename)

            # 使用每日盈亏分析器的保存方法
            self.daily_pnl_analyzer.save_daily_series_to_excel(daily_series_data, output_path)

        except Exception as e:
            logger.error(f"保存每日序列数据失败: {str(e)}")
            raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CTA策略分析系统')
    parser.add_argument('--start-date', type=str, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--modules', nargs='+', 
                       choices=['profit_loss_analysis', 'position_analysis', 'returns_calculation', 
                               'performance_risk_analysis', 'contribution_analysis'],
                       help='要运行的分析模块')
    parser.add_argument('--report-type', type=str, default='all',
                       choices=['today', 'this_year', 'custom_period', 'all'],
                       help='报告类型')
    parser.add_argument('--config-dir', type=str, default='config',
                       help='配置文件目录')
    
    args = parser.parse_args()
    
    try:
        # 初始化分析系统
        logger.info("初始化CTA策略分析系统...")
        analysis_system = CTAAnalysisSystem(args.config_dir)
        
        # 运行完整分析
        logger.info("开始执行完整分析流程...")
        results = analysis_system.run_full_analysis(
            start_date=args.start_date,
            end_date=args.end_date,
            modules=args.modules,
            report_type=args.report_type
        )
        
        # 输出结果摘要
        summary = results['summary']
        logger.info("="*50)
        logger.info("分析完成摘要:")
        logger.info(f"- 执行的分析模块数: {summary['analysis_modules']}")
        logger.info(f"- 生成的报告文件数: {summary['report_files']}")
        logger.info(f"- 执行完成时间: {summary['execution_time']}")
        logger.info("="*50)
        
        # 输出报告文件路径
        logger.info("生成的报告文件:")
        for report_name, report_path in results['report_paths'].items():
            logger.info(f"- {report_name}: {report_path}")
        
        logger.info("CTA策略分析系统执行完成！")
        
    except Exception as e:
        logger.error(f"系统执行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
