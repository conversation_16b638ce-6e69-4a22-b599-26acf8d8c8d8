#!/usr/bin/env python3
"""
测试重构后的盈亏分析和持仓分析模块
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from profit_loss_analysis import ProfitLossAnalyzer
from position_analysis import PositionAnalyzer
from daily_profit_loss_series import DailyProfitLossAnalyzer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    
    # 创建日期范围
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 6, 30)
    date_range = pd.date_range(start_date, end_date, freq='D')
    
    # 基础数据配置
    strategy_categories = ['Trend', 'Option', 'Other']
    strategy_signals = ['simple_rsi', 'bollinger', 'momentum']
    signal_freqs = ['daily', 'weekly', 'monthly']
    position_directions = ['Long', 'Short']
    symbol_categories = ['Equities', 'Bonds', 'Commodities_Energy', 'Commodities_Metal']
    industries = ['Financial', 'Tech', 'Agriculture', 'Industrial']
    product_names = ['IF2506', 'IC2506', 'IH2506', 'T2506', 'TF2506', 'CU2506', 'AL2506', 'SC2506']
    
    data = []
    
    for date in date_range:
        # 每天生成多条记录
        num_records = np.random.randint(10, 50)
        
        for i in range(num_records):
            record = {
                'cx_id': f"CX{date.strftime('%Y%m%d')}{i:03d}",
                'trade_date': date,
                'product_name': np.random.choice(product_names),
                'symbol': f"SYM{i:03d}",
                'position_direction': np.random.choice(position_directions),
                'symbol_category': np.random.choice(symbol_categories),
                'contract_unit': 100,
                'profit_loss_amount': np.random.normal(1000, 5000),  # 正负盈亏
                'strategy_signal': np.random.choice(strategy_signals),
                'signal_freq': np.random.choice(signal_freqs),
                'strategy_category': np.random.choice(strategy_categories),
                'industry': np.random.choice(industries),
                'position_amount': np.random.uniform(50000, 500000)  # 持仓金额
            }
            data.append(record)
    
    df = pd.DataFrame(data)
    logger.info(f"创建测试数据完成，共 {len(df)} 条记录")
    return df

def load_test_config():
    """加载测试配置"""
    config_path = os.path.join('config', 'analysis_config.json')
    
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    else:
        # 使用默认配置
        config = {
            "profit_loss_analysis": {
                "primary_categories": ["strategy_category", "strategy_signal", "signal_freq"],
                "pivot_fields": ["position_direction", "symbol_category", "industry"],
                "aggregation_method": "sum",
                "custom_calculations": {
                    "symbol_category_mapping": {
                        "CustomCategory1": ["Equities", "Bonds"],
                        "CustomCategory2": ["Commodities_Energy", "Commodities_Metal"]
                    },
                    "industry_mapping": {
                        "CustomIndustryA": ["Financial", "Tech"],
                        "CustomIndustryB": ["Agriculture", "Industrial"]
                    }
                }
            },
            "daily_profit_loss_series_config": {
                "daily_pivot_fields_level_1": ["strategy_category", "strategy_signal", "signal_freq", "position_direction", "symbol_category", "industry"],
                "filter_fields_level_2_primary": ["strategy_category", "strategy_signal", "signal_freq"],
                "pivot_fields_level_2_secondary": ["position_direction", "symbol_category", "industry"]
            },
            "position_analysis": {
                "concentration_fields": ["symbol_category", "industry"],
                "strategy_categories": ["Trend", "Option", "Other"]
            }
        }
    
    return config

def test_profit_loss_analysis():
    """测试盈亏分析模块"""
    logger.info("开始测试盈亏分析模块...")
    
    # 创建测试数据和配置
    test_data = create_test_data()
    config = load_test_config()
    
    # 初始化分析器
    analyzer = ProfitLossAnalyzer(config)
    
    # 测试时间筛选
    start_date = datetime(2025, 3, 1)
    end_date = datetime(2025, 3, 31)
    
    # 执行期间汇总分析
    pnl_results = analyzer.analyze_profit_loss(test_data, start_date, end_date)

    # 验证结果
    logger.info(f"盈亏分析结果模块数量: {len(pnl_results)}")

    if 'primary_category_summary' in pnl_results:
        logger.info(f"一级分类汇总结果数量: {len(pnl_results['primary_category_summary'])}")
        if pnl_results['primary_category_summary']:
            logger.info("一级分类汇总结果示例:")
            logger.info(pnl_results['primary_category_summary'][0]['data'].head())

    if 'pivot_fields_summary' in pnl_results:
        logger.info(f"交叉透视汇总结果数量: {len(pnl_results['pivot_fields_summary'])}")

    # 测试每日序列数据生成
    daily_analyzer = DailyProfitLossAnalyzer(config)
    daily_series_data = daily_analyzer.generate_comprehensive_daily_pivot(test_data, start_date, end_date)

    logger.info(f"每日序列数据维度数量: {len(daily_series_data)}")
    if daily_series_data:
        first_key = list(daily_series_data.keys())[0]
        logger.info(f"每日序列数据示例 ({first_key}):")
        logger.info(daily_series_data[first_key].head())
    
    logger.info("盈亏分析模块测试完成")
    return True

def test_position_analysis():
    """测试持仓分析模块"""
    logger.info("开始测试持仓分析模块...")
    
    # 创建测试数据和配置
    test_data = create_test_data()
    config = load_test_config()
    
    # 初始化分析器
    analyzer = PositionAnalyzer(config)
    
    # 测试指定日期分析
    analysis_date = datetime(2025, 3, 15)
    
    # 执行分析
    results = analyzer.analyze_positions(test_data, analysis_date)
    
    # 验证结果
    logger.info(f"持仓分析结果模块数量: {len(results)}")
    
    if 'concentration_analysis' in results:
        concentration_results = results['concentration_analysis']
        logger.info(f"集中度分析结果数量: {len(concentration_results)}")
        
        if 'overall' in concentration_results:
            overall_concentration = concentration_results['overall']
            logger.info("整体集中度分析示例:")
            logger.info(overall_concentration.head())
    
    logger.info("持仓分析模块测试完成")
    return True

def main():
    """主测试函数"""
    logger.info("开始测试重构后的模块...")
    
    try:
        # 测试盈亏分析模块
        test_profit_loss_analysis()
        
        # 测试持仓分析模块
        test_position_analysis()
        
        logger.info("所有模块测试完成！")
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        raise

if __name__ == "__main__":
    main()
