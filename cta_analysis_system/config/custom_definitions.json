{"returns_calculation": {"custom_mappings": [{"profit_loss_target": "strategy_category:trend", "investment_scale": "trend", "description": "趋势策略收益率计算"}, {"profit_loss_target": "strategy_category:option", "investment_scale": "option", "description": "期权策略收益率计算"}, {"profit_loss_target": "strategy_category:other", "investment_scale": "other", "description": "其他策略收益率计算"}, {"profit_loss_target": "custom_symbols:StockIndexFutures", "investment_scale": "fin_trend", "description": "股指期货收益率计算"}, {"profit_loss_target": "custom_symbols:TreasuryBonds", "investment_scale": "fin_trend", "description": "国债期货收益率计算"}, {"profit_loss_target": "strategy_signal:simple_boll", "investment_scale": "trend_boll", "description": "布林带策略收益率计算"}, {"profit_loss_target": "strategy_signal:simple_rsi", "investment_scale": "trend_rsi", "description": "RSI策略收益率计算"}], "symbol_groups": {"StockIndexFutures": ["IF", "IM", "IC", "IH"], "TreasuryBonds": ["T", "TS", "TF", "TL"], "CommodityEnergy": ["SC", "FU", "LU", "PG"], "CommodityMetal": ["CU", "AL", "ZN", "PB", "NI", "SN", "AU", "AG"]}, "calculation_settings": {"replace_mode": true, "output_directory": "reports/returns_data", "file_naming_pattern": "returns_{target}_{start_date}_{end_date}.csv", "date_format": "%Y%m%d"}}, "risk_analysis": {"var_confidence_levels": [0.95, 0.99], "es_confidence_levels": [0.95, 0.99], "rolling_window": 250, "bootstrap_samples": 10000, "risk_free_rate": 0.02}, "performance_metrics": {"benchmark": "000300.SH", "frequency": "daily", "metrics": ["total_return", "annual_return", "volatility", "sharpe_ratio", "max_drawdown", "calmar_ratio", "sortino_ratio"]}}