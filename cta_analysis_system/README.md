# CTA策略分析系统

一个全面的、模块化的CTA策略分析系统，支持盈亏分析、持仓分析、收益率计算、性能风险评估和贡献分析。

## 系统特性

- **模块化设计**: 每个功能模块独立，易于维护和扩展
- **配置驱动**: 通过JSON配置文件灵活控制分析参数
- **多格式报告**: 支持Markdown、HTML、Excel多种报告格式
- **专业分析**: 集成quantstats库进行专业的性能风险分析
- **可视化图表**: 自动生成高质量的分析图表
- **中间数据**: 保存详细的中间分析数据供进一步研究

## 系统架构

```
cta_analysis_system/
├── input/                          # 输入数据目录
│   ├── processed_cta_data.csv      # CTA策略每日盈亏数据
│   └── strategy_position.csv       # 策略每日投入资金数据
├── config/                         # 配置文件目录
│   ├── analysis_config.json        # 分析模块配置
│   ├── report_config.json          # 报告模块配置
│   └── custom_definitions.json     # 自定义计算配置
├── modules/                        # 分析模块目录
│   ├── data_loader.py              # 数据加载与预处理
│   ├── profit_loss_analysis.py     # 盈亏分析模块
│   ├── position_analysis.py        # 持仓分析模块
│   ├── returns_calculation.py      # 收益率计算模块
│   ├── performance_risk_analysis.py # 性能风险分析模块
│   ├── contribution_analysis.py    # 贡献分析模块
│   └── report_generator.py         # 报告生成模块
├── reports/                        # 报告输出目录
│   ├── daily/                      # 每日报告
│   ├── yearly/                     # 年度报告
│   ├── custom_period/              # 自定义时间段报告
│   └── returns_data/               # 收益率数据
├── plots/                          # 图表输出目录
├── intermediate_data/              # 中间数据目录
│   ├── risk_analysis/              # 风险分析中间数据
│   └── drawdown_analysis/          # 回撤分析中间数据
├── main.py                         # 主运行脚本
├── requirements.txt                # 依赖包列表
└── README.md                       # 说明文档
```

## 安装和配置

### 1. 环境要求

- Python 3.8+
- 推荐使用虚拟环境

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 数据准备

将您的数据文件放置在 `input/` 目录下：

#### processed_cta_data.csv 字段说明：
- `cx_id`: 交易ID
- `trade_date`: 交易日期
- `product_name`: 产品名称
- `symbol`: 合约代码
- `position_direction`: 持仓方向 (Long/Short)
- `symbol_category`: 合约类别
- `contract_unit`: 合约单位
- `profit_loss_amount`: 盈亏金额
- `strategy_signal`: 策略信号
- `signal_freq`: 信号频率
- `strategy_category`: 策略类别
- `industry`: 行业
- `position_amount`: 持仓金额

#### strategy_position.csv 字段说明：
- `data`: 日期 (将自动重命名为trade_date)
- `other`: Other策略投入规模
- `option`: Option策略投入规模
- `trend`: Trend策略投入规模
- `fin`: 金融期货投入规模
- `boll`: 布林带策略投入规模
- `rsi`: RSI策略投入规模

### 4. 配置文件说明

#### analysis_config.json
控制分析模块的行为，包括：
- 盈亏分析的一级分类字段
- 透视分析字段
- 自定义分类映射
- 可视化配置

#### report_config.json
控制报告生成的配置，包括：
- 各类报告的启用状态
- 包含的分析模块
- 输出格式设置
- 图表设置

#### custom_definitions.json
自定义计算配置，包括：
- 收益率计算映射规则
- 合约组定义
- 风险分析参数
- 性能指标设置

## 使用方法

### 基本使用

```bash
# 运行完整分析（使用默认配置）
python main.py

# 指定时间范围
python main.py --start-date 2025-01-01 --end-date 2025-06-24

# 只运行特定模块
python main.py --modules profit_loss_analysis position_analysis

# 只生成特定类型报告
python main.py --report-type today
```

### 命令行参数

- `--start-date`: 开始日期 (YYYY-MM-DD格式)
- `--end-date`: 结束日期 (YYYY-MM-DD格式)
- `--modules`: 要运行的分析模块列表
  - `profit_loss_analysis`: 盈亏分析
  - `position_analysis`: 持仓分析
  - `returns_calculation`: 收益率计算
  - `performance_risk_analysis`: 性能风险分析
  - `contribution_analysis`: 贡献分析
- `--report-type`: 报告类型
  - `today`: 今日报告
  - `this_year`: 年度报告
  - `custom_period`: 自定义期间报告
  - `all`: 所有类型报告（默认）
- `--config-dir`: 配置文件目录路径

### 编程接口使用

```python
from main import CTAAnalysisSystem

# 初始化系统
system = CTAAnalysisSystem()

# 运行分析
results = system.run_analysis(
    start_date='2025-01-01',
    end_date='2025-06-24',
    modules=['profit_loss_analysis', 'returns_calculation']
)

# 生成报告
report_paths = system.generate_reports(report_type='all')
```

## 分析模块详解

### 1. 盈亏分析模块
- **多维度透视汇总**: 按策略类别、策略信号、信号频率进行透视分析
- **自定义分类分析**: 支持自定义合约类别和行业映射
- **时间维度分析**: 按日、周、月进行盈亏汇总
- **总体统计**: 计算胜率、盈亏比等关键指标

### 2. 持仓分析模块
- **集中度分析**: 计算各维度的持仓集中度
- **行业贡献分析**: 分析各行业的持仓市值贡献比例
- **风险敞口分析**: 计算多头、空头、净敞口
- **持仓分布统计**: 持仓规模的统计分析

### 3. 收益率计算模块
- **策略收益率**: 按策略类别计算收益率序列
- **合约组收益率**: 按自定义合约组计算收益率
- **信号收益率**: 按策略信号计算收益率
- **性能指标**: 计算夏普比率、最大回撤等指标

### 4. 性能风险分析模块
- **quantstats集成**: 使用专业库进行性能分析
- **风险指标**: VaR、ES、波动率等风险度量
- **回撤分析**: 详细的回撤期间和恢复时间分析
- **可视化图表**: 累计收益、回撤、分布等图表

### 5. 贡献分析模块
- **贡献率计算**: 按总盈利/总亏损计算贡献率
- **多维度贡献**: 策略、行业、合约等维度的贡献分析
- **可视化展示**: 条形图、瀑布图等贡献分析图表

## 输出文件说明

### 报告文件
- **Markdown报告**: 结构化的文本报告，易于阅读和分享
- **HTML报告**: 交互式网页报告，包含样式和图表
- **Excel报告**: 多工作表Excel文件，便于数据分析

### 图表文件
- **累计收益曲线**: 策略收益表现的时间序列图
- **回撤分析图**: 回撤期间和幅度的可视化
- **收益率分布**: 收益率的统计分布直方图
- **贡献分析图**: 各维度贡献的条形图和瀑布图

### 中间数据
- **风险分析数据**: VaR、ES等风险指标的详细数据
- **回撤分析数据**: 回撤期间的详细统计信息
- **收益率序列**: 各策略的日收益率时间序列

## 自定义和扩展

### 添加新的分析维度
1. 在 `analysis_config.json` 中添加新的分类字段
2. 更新相应的分析模块代码
3. 在报告生成器中添加新维度的格式化方法

### 添加新的收益率计算规则
1. 在 `custom_definitions.json` 中添加新的映射规则
2. 定义新的合约组或策略组合
3. 系统将自动应用新规则进行计算

### 自定义可视化样式
1. 修改 `analysis_config.json` 中的可视化配置
2. 调整颜色方案、字体设置等
3. 系统支持跨平台的中文字体配置

## 故障排除

### 常见问题

1. **数据文件格式错误**
   - 检查CSV文件的字段名称和数据格式
   - 确保日期字段为标准格式 (YYYY-MM-DD)

2. **依赖包安装失败**
   - 使用国内镜像源：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`
   - 检查Python版本是否符合要求

3. **图表显示异常**
   - 检查系统是否安装了中文字体
   - 在配置文件中调整字体设置

4. **内存不足**
   - 对于大数据集，考虑分批处理
   - 调整分析时间范围

### 日志文件
系统运行时会生成 `cta_analysis.log` 日志文件，包含详细的执行信息和错误信息。

## 技术支持

如有问题或建议，请查看日志文件或联系开发团队。

## 版本信息

- 当前版本: 1.0.0
- 最后更新: 2025-06-24
- Python版本要求: 3.8+
