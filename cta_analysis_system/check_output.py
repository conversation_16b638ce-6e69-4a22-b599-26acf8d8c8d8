#!/usr/bin/env python3
"""
检查重构后的输出结果
"""

import pandas as pd
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_daily_series_excel():
    """检查每日序列数据Excel文件"""
    excel_path = "output/daily_profit_loss_series.xlsx"
    
    if not os.path.exists(excel_path):
        logger.error(f"Excel文件不存在: {excel_path}")
        return
    
    logger.info(f"检查Excel文件: {excel_path}")
    
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(excel_path)
        sheet_names = excel_file.sheet_names
        
        logger.info(f"Excel文件包含 {len(sheet_names)} 个工作表:")
        for i, sheet_name in enumerate(sheet_names, 1):
            logger.info(f"  {i}. {sheet_name}")
        
        # 检查每个工作表的内容
        for sheet_name in sheet_names[:3]:  # 只检查前3个工作表
            logger.info(f"\n--- 工作表: {sheet_name} ---")
            df = pd.read_excel(excel_path, sheet_name=sheet_name)
            logger.info(f"数据形状: {df.shape}")
            logger.info(f"列名: {list(df.columns)}")
            
            if not df.empty:
                logger.info("前5行数据:")
                logger.info(df.head().to_string())
            
            # 检查是否包含'all'或'total'列
            total_cols = [col for col in df.columns if 'all' in str(col).lower() or 'total' in str(col).lower()]
            if total_cols:
                logger.info(f"包含汇总列: {total_cols}")
        
    except Exception as e:
        logger.error(f"读取Excel文件失败: {str(e)}")

def check_reports():
    """检查生成的报告文件"""
    report_dirs = ["reports/daily", "reports/yearly", "reports/custom_period"]
    
    for report_dir in report_dirs:
        if os.path.exists(report_dir):
            files = os.listdir(report_dir)
            logger.info(f"\n{report_dir} 目录包含 {len(files)} 个文件:")
            for file in files:
                file_path = os.path.join(report_dir, file)
                file_size = os.path.getsize(file_path)
                logger.info(f"  - {file} ({file_size} bytes)")

def main():
    """主函数"""
    logger.info("开始检查重构后的输出结果...")
    
    # 检查每日序列数据Excel文件
    check_daily_series_excel()
    
    # 检查报告文件
    check_reports()
    
    logger.info("\n输出结果检查完成！")

if __name__ == "__main__":
    main()
