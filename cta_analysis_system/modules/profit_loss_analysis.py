"""
CTA策略盈亏分析模块（重构版）
实现多维度盈亏分析，包括单一维度、组合维度和自定义组合分析
"""

import pandas as pd
import numpy as np
import logging
import os
from typing import Dict, List, Any, Tuple, Optional, Union
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class ProfitLossAnalyzer:
    """CTA策略盈亏分析器类（重构版）"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化盈亏分析器

        Args:
            config: 分析配置字典，包含dimensions_for_analysis、preset_combinations等
        """
        # 从新的配置结构中获取参数
        self.dimensions_for_analysis = config.get('dimensions_for_analysis', [
            "strategy_category", "strategy_signal", "signal_freq",
            "position_direction", "symbol_category", "industry"
        ])
        self.top_bottom_n = config.get('top_bottom_n', 5)
        self.preset_combinations = config.get('preset_combinations', [])
        self.custom_combinations = config.get('custom_combinations', [])

        # 保持向后兼容性
        self.aggregation_method = config.get('aggregation_method', 'sum')

        logger.info(f"盈亏分析器初始化完成，分析维度: {len(self.dimensions_for_analysis)}个，预设组合: {len(self.preset_combinations)}个")

    def analyze_profit_loss(self, cta_data: pd.DataFrame, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        执行指定期间的汇总盈亏分析（不包含每日序列数据）。

        Args:
            cta_data (pd.DataFrame): 预处理后的CTA每日盈亏数据。
            start_date (datetime, optional): 分析的开始日期。默认为None，表示不进行开始日期筛选。
            end_date (datetime, optional): 分析的结束日期。默认为None，表示不进行结束日期筛选。

        Returns:
            dict: 包含所有汇总盈亏分析结果的字典。
                  示例:
                  {
                      "primary_category_summary": list,     # 一级分类汇总结果列表
                      "pivot_fields_summary": list,         # 交叉透视汇总结果列表
                      "custom_symbol_category_summary": pd.DataFrame,  # 自定义合约类别盈亏
                      "custom_industry_summary": pd.DataFrame          # 自定义行业盈亏
                  }
        """
        try:
            # 1. 时间筛选（提取通用逻辑）
            filtered_df = cta_data.copy()
            if start_date:
                filtered_df = filtered_df[filtered_df['trade_date'] >= start_date]
            if end_date:
                filtered_df = filtered_df[filtered_df['trade_date'] <= end_date]

            if filtered_df.empty:
                logger.warning(f"期间 {start_date} 到 {end_date} 无盈亏数据可供分析。")
                return {}

            logger.info(f"时间筛选后数据量: {len(filtered_df)} 条记录")

            results = {}

            # 1.2 新建透视表（一级分类字段汇总盈亏）
            results["primary_category_summary"] = self._primary_categories_summary(filtered_df)

            # 1.3 新建透视表（primary_categories 作为筛选维度，pivot_fields 字段透视汇总）
            results["pivot_fields_summary"] = self._pivot_fields_summary(filtered_df)

            # 1.5 自定义功能（期间汇总盈亏）
            custom_results = self._custom_category_summary(filtered_df)
            results.update(custom_results)

            logger.info("期间汇总盈亏分析完成")
            return results

        except Exception as e:
            logger.error(f"盈亏分析失败: {str(e)}")
            raise

    def _primary_categories_summary(self, filtered_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        1.2 一级分类字段汇总盈亏

        Args:
            filtered_df: 时间筛选后的数据

        Returns:
            一级分类汇总结果列表
        """
        primary_summaries = []

        for category_field in self.primary_categories:
            if category_field not in filtered_df.columns:
                logger.warning(f"数据中缺少字段: {category_field}")
                continue

            # 按一级分类字段分组并汇总
            summary_df = filtered_df.groupby(category_field)['profit_loss_amount'].agg(self.aggregation_method).reset_index()
            summary_df.rename(columns={'profit_loss_amount': 'Total_Profit_Loss'}, inplace=True)

            primary_summaries.append({
                "category_field": category_field,
                "data": summary_df
            })

            logger.info(f"完成一级分类汇总: {category_field}")

        return primary_summaries

    def _pivot_fields_summary(self, filtered_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        1.3 交叉透视汇总（primary_categories 作为筛选维度，pivot_fields 字段透视汇总）

        Args:
            filtered_df: 时间筛选后的数据

        Returns:
            交叉透视汇总结果列表
        """
        pivot_field_summaries = []

        for primary_field in self.primary_categories:
            if primary_field not in filtered_df.columns:
                continue

            unique_primary_values = filtered_df[primary_field].unique()
            for primary_value in unique_primary_values:
                subset_df = filtered_df[filtered_df[primary_field] == primary_value].copy()
                if subset_df.empty:
                    continue

                # 创建交叉透视表（整个期间的汇总）
                if len(self.pivot_fields) > 1:
                    # 多字段透视
                    pivot_result = pd.pivot_table(subset_df,
                                                  values='profit_loss_amount',
                                                  index=[primary_field],
                                                  columns=self.pivot_fields,
                                                  aggfunc=self.aggregation_method,
                                                  fill_value=0)

                    # 展平多级列索引
                    if isinstance(pivot_result.columns, pd.MultiIndex):
                        pivot_result.columns = ['_'.join(map(str, col)).strip() for col in pivot_result.columns.values]
                else:
                    # 单字段透视
                    pivot_result = subset_df.groupby(self.pivot_fields[0])['profit_loss_amount'].agg(self.aggregation_method).reset_index()

                # 添加主要筛选字段值作为列
                pivot_result.insert(0, primary_field, primary_value)

                pivot_field_summaries.append({
                    "primary_filter_field": primary_field,
                    "primary_filter_value": primary_value,
                    "data": pivot_result.reset_index(drop=True)
                })

        logger.info(f"完成交叉透视汇总，共生成 {len(pivot_field_summaries)} 个结果")
        return pivot_field_summaries



    def _multi_dimension_pivot(self, cta_data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        多维度盈亏透视汇总
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            各维度透视结果
        """
        pivot_results = {}
        
        for primary_category in self.primary_categories:
            if primary_category not in cta_data.columns:
                logger.warning(f"数据中缺少字段: {primary_category}")
                continue
            
            category_results = {}
            
            # 获取该一级分类的所有唯一值
            unique_values = cta_data[primary_category].unique()
            
            for value in unique_values:
                # 过滤数据
                filtered_data = cta_data[cta_data[primary_category] == value]
                
                # 对每个透视字段进行汇总
                pivot_data = {}
                for pivot_field in self.pivot_fields:
                    if pivot_field in filtered_data.columns:
                        pivot_summary = filtered_data.groupby(pivot_field)['profit_loss_amount'].agg([
                            'sum', 'mean', 'count', 'std'
                        ]).round(2)
                        pivot_summary.columns = ['总盈亏', '平均盈亏', '交易次数', '盈亏标准差']
                        pivot_data[pivot_field] = pivot_summary
                
                category_results[f"{primary_category}_{value}"] = pivot_data
            
            pivot_results[primary_category] = category_results
        
        return pivot_results
    
    def _custom_category_summary(self, filtered_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        自定义分类期间汇总盈亏分析

        Args:
            filtered_df: 时间筛选后的数据

        Returns:
            自定义分类汇总结果
        """
        custom_results = {}

        # 自定义合约类别盈亏
        symbol_category_mapping = self.custom_calculations.get('symbol_category_mapping', {})
        custom_symbol_category_pl_data = []
        for custom_cat, symbols_list in symbol_category_mapping.items():
            current_category_pl = filtered_df[filtered_df['symbol_category'].isin(symbols_list)]['profit_loss_amount'].agg(self.aggregation_method)
            custom_symbol_category_pl_data.append({'CustomCategory': custom_cat, 'Total_Profit_Loss': current_category_pl})
        custom_results["custom_symbol_category_summary"] = pd.DataFrame(custom_symbol_category_pl_data)

        # 自定义行业盈亏
        industry_mapping = self.custom_calculations.get('industry_mapping', {})
        custom_industry_pl_data = []
        for custom_ind, industries_list in industry_mapping.items():
            current_industry_pl = filtered_df[filtered_df['industry'].isin(industries_list)]['profit_loss_amount'].agg(self.aggregation_method)
            custom_industry_pl_data.append({'CustomIndustry': custom_ind, 'Total_Profit_Loss': current_industry_pl})
        custom_results["custom_industry_summary"] = pd.DataFrame(custom_industry_pl_data)

        return custom_results
    
    def _apply_custom_mapping(self, data: pd.DataFrame, field: str, mapping: Dict[str, List[str]]) -> pd.DataFrame:
        """
        应用自定义映射
        
        Args:
            data: 原始数据
            field: 要映射的字段
            mapping: 映射规则
            
        Returns:
            应用映射后的数据
        """
        if field not in data.columns:
            logger.warning(f"数据中缺少字段: {field}")
            return pd.DataFrame()
        
        # 创建映射字典
        value_to_category = {}
        for category, values in mapping.items():
            for value in values:
                value_to_category[value] = category
        
        # 应用映射
        mapped_data = data.copy()
        mapped_data['custom_category'] = mapped_data[field].map(value_to_category)
        
        # 过滤掉未映射的数据
        mapped_data = mapped_data.dropna(subset=['custom_category'])
        
        return mapped_data
    
    def _calculate_overall_statistics(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """
        计算总体盈亏统计
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            总体统计结果
        """
        stats = {}
        
        # 基本统计
        stats['总盈亏'] = cta_data['profit_loss_amount'].sum()
        stats['平均每日盈亏'] = cta_data.groupby('trade_date')['profit_loss_amount'].sum().mean()
        stats['总交易次数'] = len(cta_data)
        stats['盈利交易次数'] = len(cta_data[cta_data['profit_loss_amount'] > 0])
        stats['亏损交易次数'] = len(cta_data[cta_data['profit_loss_amount'] < 0])
        stats['胜率'] = stats['盈利交易次数'] / stats['总交易次数'] * 100 if stats['总交易次数'] > 0 else 0
        
        # 盈亏分布
        stats['最大单笔盈利'] = cta_data['profit_loss_amount'].max()
        stats['最大单笔亏损'] = cta_data['profit_loss_amount'].min()
        stats['盈亏标准差'] = cta_data['profit_loss_amount'].std()
        
        # 按策略类别统计
        if 'strategy_category' in cta_data.columns:
            strategy_stats = cta_data.groupby('strategy_category')['profit_loss_amount'].agg([
                'sum', 'mean', 'count'
            ]).round(2)
            strategy_stats.columns = ['总盈亏', '平均盈亏', '交易次数']
            stats['策略类别统计'] = strategy_stats
        
        return stats
    
    def _time_dimension_analysis(self, cta_data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        按时间维度分析盈亏
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            时间维度分析结果
        """
        time_results = {}
        
        # 按日期汇总
        daily_pnl = cta_data.groupby('trade_date')['profit_loss_amount'].sum().reset_index()
        daily_pnl.columns = ['交易日期', '当日盈亏']
        daily_pnl['累计盈亏'] = daily_pnl['当日盈亏'].cumsum()
        time_results['daily_summary'] = daily_pnl
        
        # 按月汇总
        cta_data_copy = cta_data.copy()
        cta_data_copy['year_month'] = cta_data_copy['trade_date'].dt.to_period('M')
        monthly_pnl = cta_data_copy.groupby('year_month')['profit_loss_amount'].agg([
            'sum', 'mean', 'count'
        ]).round(2)
        monthly_pnl.columns = ['月度盈亏', '平均每日盈亏', '交易天数']
        time_results['monthly_summary'] = monthly_pnl
        
        # 按周汇总
        cta_data_copy['year_week'] = cta_data_copy['trade_date'].dt.to_period('W')
        weekly_pnl = cta_data_copy.groupby('year_week')['profit_loss_amount'].agg([
            'sum', 'mean', 'count'
        ]).round(2)
        weekly_pnl.columns = ['周度盈亏', '平均每日盈亏', '交易天数']
        time_results['weekly_summary'] = weekly_pnl
        
        return time_results
    
    def generate_summary_report(self, analysis_results: Dict[str, Any]) -> str:
        """
        生成盈亏分析摘要报告
        
        Args:
            analysis_results: 分析结果
            
        Returns:
            摘要报告文本
        """
        report_lines = []
        report_lines.append("# 盈亏分析报告")
        report_lines.append("")
        
        # 总体统计
        if 'overall_statistics' in analysis_results:
            stats = analysis_results['overall_statistics']
            report_lines.append("## 总体统计")
            report_lines.append(f"- 总盈亏: {stats.get('总盈亏', 0):,.2f} 元")
            report_lines.append(f"- 平均每日盈亏: {stats.get('平均每日盈亏', 0):,.2f} 元")
            report_lines.append(f"- 总交易次数: {stats.get('总交易次数', 0):,} 次")
            report_lines.append(f"- 胜率: {stats.get('胜率', 0):.2f}%")
            report_lines.append("")
        
        # 多维度分析摘要
        if 'multi_dimension_analysis' in analysis_results:
            report_lines.append("## 多维度分析摘要")
            multi_dim = analysis_results['multi_dimension_analysis']
            for category, results in multi_dim.items():
                report_lines.append(f"### {category}")
                for subcategory, pivot_data in results.items():
                    report_lines.append(f"- {subcategory}: 包含 {len(pivot_data)} 个透视维度")
                report_lines.append("")
        
        return "\n".join(report_lines)
