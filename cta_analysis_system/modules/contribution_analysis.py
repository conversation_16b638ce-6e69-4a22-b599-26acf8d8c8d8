"""
贡献分析模块
负责计算各维度的收益贡献率分析
"""

import pandas as pd
import numpy as np
import logging
import matplotlib.pyplot as plt
import seaborn as sns
import os
from typing import Dict, List, Any, Tuple

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'PingFang SC', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class ContributionAnalyzer:
    """贡献分析器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化贡献分析器
        
        Args:
            config: 分析配置字典
        """
        self.config = config
        self.visualization_config = config.get('visualization', {})
        self.colors = self.visualization_config.get('colors', {
            'profit': '#00AA00',
            'loss': '#FF0000',
            'neutral': '#0066CC'
        })
        
    def analyze_contributions(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """
        执行完整的贡献分析
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            贡献分析结果字典
        """
        results = {}
        
        try:
            # 1. 策略类别贡献分析
            if 'strategy_category' in cta_data.columns:
                results['strategy_category_contribution'] = self._calculate_contribution(
                    cta_data, 'strategy_category', '策略类别'
                )
            
            # 2. 策略信号贡献分析
            if 'strategy_signal' in cta_data.columns:
                results['strategy_signal_contribution'] = self._calculate_contribution(
                    cta_data, 'strategy_signal', '策略信号'
                )
            
            # 3. 信号频率贡献分析
            if 'signal_freq' in cta_data.columns:
                results['signal_freq_contribution'] = self._calculate_contribution(
                    cta_data, 'signal_freq', '信号频率'
                )
            
            # 4. 行业贡献分析
            if 'industry' in cta_data.columns:
                results['industry_contribution'] = self._calculate_contribution(
                    cta_data, 'industry', '行业'
                )
            
            # 5. 合约类别贡献分析
            if 'symbol_category' in cta_data.columns:
                results['symbol_category_contribution'] = self._calculate_contribution(
                    cta_data, 'symbol_category', '合约类别'
                )
            
            # 6. 多空方向贡献分析
            if 'position_direction' in cta_data.columns:
                results['position_direction_contribution'] = self._calculate_contribution(
                    cta_data, 'position_direction', '多空方向'
                )
            
            # 7. 生成贡献分析图表
            results['contribution_charts'] = self._generate_contribution_charts(results)
            
            # 8. 综合贡献分析摘要
            results['contribution_summary'] = self._generate_contribution_summary(results)
            
            logger.info("贡献分析完成")
            return results
            
        except Exception as e:
            logger.error(f"贡献分析失败: {str(e)}")
            raise
    
    def _calculate_contribution(self, data: pd.DataFrame, group_field: str, 
                              field_description: str) -> Dict[str, Any]:
        """
        计算指定字段的贡献率
        
        Args:
            data: 数据集
            group_field: 分组字段
            field_description: 字段描述
            
        Returns:
            贡献分析结果
        """
        # 按字段分组汇总盈亏
        group_pnl = data.groupby(group_field)['profit_loss_amount'].sum()
        
        # 分离盈利和亏损
        profit_items = group_pnl[group_pnl > 0]
        loss_items = group_pnl[group_pnl < 0]
        
        total_profit = profit_items.sum()
        total_loss = loss_items.sum()
        total_pnl = group_pnl.sum()
        
        contribution_data = []
        
        # 计算贡献率
        for category, pnl_value in group_pnl.items():
            if pnl_value > 0:
                # 盈利项目
                if total_profit > 0:
                    contribution_rate = pnl_value / total_profit
                else:
                    contribution_rate = 0
                contribution_type = '盈利贡献'
            elif pnl_value < 0:
                # 亏损项目
                if total_loss < 0:
                    contribution_rate = pnl_value / total_loss  # 保持负号
                else:
                    contribution_rate = 0
                contribution_type = '亏损贡献'
            else:
                contribution_rate = 0
                contribution_type = '无贡献'
            
            contribution_data.append({
                '类别': category,
                '盈亏金额': pnl_value,
                '贡献率': contribution_rate,
                '贡献类型': contribution_type,
                '绝对贡献率': abs(contribution_rate)
            })
        
        contribution_df = pd.DataFrame(contribution_data)
        
        # 计算统计信息
        statistics = {
            '总盈亏': total_pnl,
            '总盈利': total_profit,
            '总亏损': total_loss,
            '盈利项目数': len(profit_items),
            '亏损项目数': len(loss_items),
            '盈利项目占比': len(profit_items) / len(group_pnl) if len(group_pnl) > 0 else 0,
            '亏损项目占比': len(loss_items) / len(group_pnl) if len(group_pnl) > 0 else 0
        }
        
        return {
            'field_description': field_description,
            'contribution_data': contribution_df,
            'statistics': statistics,
            'group_field': group_field
        }
    
    def _generate_contribution_charts(self, contribution_results: Dict[str, Any]) -> Dict[str, str]:
        """
        生成贡献分析图表
        
        Args:
            contribution_results: 贡献分析结果
            
        Returns:
            图表文件路径字典
        """
        chart_paths = {}
        
        try:
            # 创建图表输出目录
            charts_dir = "plots"
            os.makedirs(charts_dir, exist_ok=True)
            
            for analysis_type, result in contribution_results.items():
                if not isinstance(result, dict) or 'contribution_data' not in result:
                    continue
                
                contribution_data = result['contribution_data']
                field_description = result['field_description']
                
                if contribution_data.empty:
                    continue
                
                # 1. 贡献率条形图
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
                
                # 分离盈利和亏损数据
                profit_data = contribution_data[contribution_data['贡献类型'] == '盈利贡献']
                loss_data = contribution_data[contribution_data['贡献类型'] == '亏损贡献']
                
                # 盈利贡献图
                if not profit_data.empty:
                    profit_sorted = profit_data.sort_values('贡献率', ascending=True)
                    bars1 = ax1.barh(profit_sorted['类别'], profit_sorted['贡献率'], 
                                   color=self.colors['profit'], alpha=0.7)
                    ax1.set_title(f'{field_description} - 盈利贡献率', fontsize=12, fontweight='bold')
                    ax1.set_xlabel('贡献率')
                    ax1.grid(True, alpha=0.3)
                    
                    # 添加数值标签
                    for bar, value in zip(bars1, profit_sorted['贡献率']):
                        ax1.text(value + 0.01, bar.get_y() + bar.get_height()/2, 
                               f'{value:.1%}', va='center', fontsize=9)
                
                # 亏损贡献图
                if not loss_data.empty:
                    loss_sorted = loss_data.sort_values('贡献率', ascending=False)
                    bars2 = ax2.barh(loss_sorted['类别'], loss_sorted['贡献率'], 
                                   color=self.colors['loss'], alpha=0.7)
                    ax2.set_title(f'{field_description} - 亏损贡献率', fontsize=12, fontweight='bold')
                    ax2.set_xlabel('贡献率')
                    ax2.grid(True, alpha=0.3)
                    
                    # 添加数值标签
                    for bar, value in zip(bars2, loss_sorted['贡献率']):
                        ax2.text(value - 0.01, bar.get_y() + bar.get_height()/2, 
                               f'{value:.1%}', va='center', ha='right', fontsize=9)
                
                plt.tight_layout()
                
                chart_path = os.path.join(charts_dir, f'{analysis_type}_contribution_bars.png')
                plt.savefig(chart_path, dpi=300, bbox_inches='tight')
                plt.close()
                chart_paths[f'{field_description}_条形图'] = chart_path
                
                # 2. 综合贡献瀑布图
                fig, ax = plt.subplots(figsize=(14, 8))
                
                # 准备瀑布图数据
                sorted_data = contribution_data.sort_values('盈亏金额', ascending=False)
                categories = sorted_data['类别'].tolist()
                values = sorted_data['盈亏金额'].tolist()
                
                # 计算累计值
                cumulative = [0]
                for i, value in enumerate(values):
                    cumulative.append(cumulative[-1] + value)
                
                # 绘制瀑布图
                colors = []
                for value in values:
                    if value > 0:
                        colors.append(self.colors['profit'])
                    elif value < 0:
                        colors.append(self.colors['loss'])
                    else:
                        colors.append(self.colors['neutral'])
                
                # 绘制条形
                for i, (category, value) in enumerate(zip(categories, values)):
                    bottom = cumulative[i] if value > 0 else cumulative[i] + value
                    ax.bar(i, abs(value), bottom=bottom, color=colors[i], alpha=0.7, 
                          edgecolor='black', linewidth=0.5)
                    
                    # 添加数值标签
                    label_y = bottom + abs(value)/2
                    ax.text(i, label_y, f'{value:,.0f}', ha='center', va='center', 
                           fontsize=9, fontweight='bold')
                
                # 添加连接线
                for i in range(len(categories)):
                    if i < len(categories) - 1:
                        ax.plot([i+0.4, i+0.6], [cumulative[i+1], cumulative[i+1]], 
                               'k--', alpha=0.5, linewidth=1)
                
                ax.set_xticks(range(len(categories)))
                ax.set_xticklabels(categories, rotation=45, ha='right')
                ax.set_title(f'{field_description} - 贡献瀑布图', fontsize=14, fontweight='bold')
                ax.set_ylabel('盈亏金额')
                ax.grid(True, alpha=0.3)
                ax.axhline(y=0, color='black', linewidth=1)
                
                plt.tight_layout()
                
                chart_path = os.path.join(charts_dir, f'{analysis_type}_waterfall.png')
                plt.savefig(chart_path, dpi=300, bbox_inches='tight')
                plt.close()
                chart_paths[f'{field_description}_瀑布图'] = chart_path
            
            logger.info(f"生成了 {len(chart_paths)} 个贡献分析图表")
            
        except Exception as e:
            logger.error(f"生成贡献分析图表失败: {str(e)}")
        
        return chart_paths
    
    def _generate_contribution_summary(self, contribution_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成贡献分析摘要
        
        Args:
            contribution_results: 贡献分析结果
            
        Returns:
            摘要信息字典
        """
        summary = {}
        
        for analysis_type, result in contribution_results.items():
            if not isinstance(result, dict) or 'contribution_data' not in result:
                continue
            
            contribution_data = result['contribution_data']
            statistics = result['statistics']
            field_description = result['field_description']
            
            if contribution_data.empty:
                continue
            
            # 找出最大贡献者
            max_profit_contributor = contribution_data[
                contribution_data['贡献类型'] == '盈利贡献'
            ].nlargest(1, '贡献率')
            
            max_loss_contributor = contribution_data[
                contribution_data['贡献类型'] == '亏损贡献'
            ].nsmallest(1, '贡献率')
            
            field_summary = {
                '字段描述': field_description,
                '总体统计': statistics,
                '最大盈利贡献者': max_profit_contributor.to_dict('records')[0] if not max_profit_contributor.empty else None,
                '最大亏损贡献者': max_loss_contributor.to_dict('records')[0] if not max_loss_contributor.empty else None,
                '贡献集中度': {
                    '前3盈利贡献率': contribution_data[contribution_data['贡献类型'] == '盈利贡献'].nlargest(3, '贡献率')['贡献率'].sum(),
                    '前3亏损贡献率': abs(contribution_data[contribution_data['贡献类型'] == '亏损贡献'].nsmallest(3, '贡献率')['贡献率'].sum())
                }
            }
            
            summary[analysis_type] = field_summary
        
        return summary
