"""
数据加载与预处理模块
负责加载CTA策略数据和持仓数据，并进行必要的预处理
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Tuple, Dict, Any
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataLoader:
    """数据加载器类"""
    
    def __init__(self, input_dir: str = "input"):
        """
        初始化数据加载器
        
        Args:
            input_dir: 输入数据目录路径
        """
        self.input_dir = input_dir
        self.cta_data = None
        self.position_data = None
        
    def load_cta_data(self, filename: str = "processed_cta_data.csv") -> pd.DataFrame:
        """
        加载CTA策略每日盈亏数据
        
        Args:
            filename: CTA数据文件名
            
        Returns:
            处理后的CTA数据DataFrame
        """
        try:
            file_path = os.path.join(self.input_dir, filename)
            
            # 检查文件是否存在，如果不存在则尝试加载示例文件
            if not os.path.exists(file_path):
                sample_file = os.path.join(self.input_dir, "processed_cta_data_sample.csv")
                if os.path.exists(sample_file):
                    file_path = sample_file
                    logger.warning(f"未找到 {filename}，使用示例文件 {sample_file}")
                else:
                    raise FileNotFoundError(f"未找到数据文件: {filename}")
            
            # 加载数据，尝试不同的编码
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                try:
                    df = pd.read_csv(file_path, encoding='utf-8-sig')
                except UnicodeDecodeError:
                    try:
                        df = pd.read_csv(file_path, encoding='gbk')
                    except UnicodeDecodeError:
                        df = pd.read_csv(file_path, encoding='latin-1')
            logger.info(f"成功加载CTA数据，共 {len(df)} 条记录")
            
            # 数据预处理
            df = self._preprocess_cta_data(df)
            
            self.cta_data = df
            return df
            
        except Exception as e:
            logger.error(f"加载CTA数据失败: {str(e)}")
            raise
    
    def load_position_data(self, filename: str = "strategy_position.csv") -> pd.DataFrame:
        """
        加载策略持仓资金规模数据
        
        Args:
            filename: 持仓数据文件名
            
        Returns:
            处理后的持仓数据DataFrame
        """
        try:
            file_path = os.path.join(self.input_dir, filename)
            
            # 检查文件是否存在，如果不存在则尝试加载示例文件
            if not os.path.exists(file_path):
                sample_file = os.path.join(self.input_dir, "strategy_position_sample.csv")
                if os.path.exists(sample_file):
                    file_path = sample_file
                    logger.warning(f"未找到 {filename}，使用示例文件 {sample_file}")
                else:
                    raise FileNotFoundError(f"未找到数据文件: {filename}")
            
            # 加载数据，尝试不同的编码
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                try:
                    df = pd.read_csv(file_path, encoding='utf-8-sig')
                except UnicodeDecodeError:
                    try:
                        df = pd.read_csv(file_path, encoding='gbk')
                    except UnicodeDecodeError:
                        df = pd.read_csv(file_path, encoding='latin-1')
            logger.info(f"成功加载持仓数据，共 {len(df)} 条记录")
            
            # 数据预处理
            df = self._preprocess_position_data(df)
            
            self.position_data = df
            return df
            
        except Exception as e:
            logger.error(f"加载持仓数据失败: {str(e)}")
            raise
    
    def _preprocess_cta_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        预处理CTA数据
        
        Args:
            df: 原始CTA数据
            
        Returns:
            预处理后的数据
        """
        # 转换日期格式
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        
        # 确保数值字段为数值类型
        numeric_columns = ['profit_loss_amount', 'position_amount', 'contract_unit']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 处理缺失值
        df = df.dropna(subset=['trade_date', 'profit_loss_amount'])
        
        # 数据验证
        self._validate_cta_data(df)
        
        logger.info("CTA数据预处理完成")
        return df
    
    def _preprocess_position_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        预处理持仓数据
        
        Args:
            df: 原始持仓数据
            
        Returns:
            预处理后的数据
        """
        # 统一日期字段名称
        if 'data' in df.columns:
            df = df.rename(columns={'data': 'trade_date'})
        elif 'date' in df.columns:
            df = df.rename(columns={'date': 'trade_date'})
        elif '﻿date' in df.columns:  # 处理BOM字符
            df = df.rename(columns={'﻿date': 'trade_date'})
        
        # 转换日期格式
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        
        # 确保投资规模字段为数值类型
        investment_columns = ['other', 'option', 'trend', 'fin', 'boll', 'rsi', 'trend_boll', 'trend_rsi']
        for col in investment_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 处理缺失值
        df = df.dropna(subset=['trade_date'])
        
        # 数据验证
        self._validate_position_data(df)
        
        logger.info("持仓数据预处理完成")
        return df
    
    def _validate_cta_data(self, df: pd.DataFrame) -> None:
        """验证CTA数据完整性"""
        required_columns = [
            'trade_date', 'symbol', 'profit_loss_amount', 
            'strategy_category', 'strategy_signal', 'signal_freq'
        ]
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"CTA数据缺少必要字段: {missing_columns}")
        
        # 检查数据范围
        if df['profit_loss_amount'].isna().any():
            logger.warning("发现盈亏金额缺失值")
        
        logger.info("CTA数据验证通过")
    
    def _validate_position_data(self, df: pd.DataFrame) -> None:
        """验证持仓数据完整性"""
        required_columns = ['trade_date']
        investment_columns = ['other', 'option', 'trend', 'fin', 'boll', 'rsi']
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"持仓数据缺少必要字段: {missing_columns}")
        
        # 检查投资规模字段
        available_investment_cols = [col for col in investment_columns if col in df.columns]
        if not available_investment_cols:
            raise ValueError("持仓数据缺少投资规模字段")
        
        logger.info("持仓数据验证通过")
    
    def get_date_range(self) -> Tuple[datetime, datetime]:
        """
        获取数据的日期范围
        
        Returns:
            (开始日期, 结束日期)
        """
        if self.cta_data is None:
            raise ValueError("请先加载CTA数据")
        
        start_date = self.cta_data['trade_date'].min()
        end_date = self.cta_data['trade_date'].max()
        
        return start_date, end_date
    
    def filter_data_by_date(self, start_date: str = None, end_date: str = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        按日期范围过滤数据
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            (过滤后的CTA数据, 过滤后的持仓数据)
        """
        if self.cta_data is None or self.position_data is None:
            raise ValueError("请先加载数据")
        
        cta_filtered = self.cta_data.copy()
        position_filtered = self.position_data.copy()
        
        if start_date:
            start_date = pd.to_datetime(start_date)
            cta_filtered = cta_filtered[cta_filtered['trade_date'] >= start_date]
            position_filtered = position_filtered[position_filtered['trade_date'] >= start_date]
        
        if end_date:
            end_date = pd.to_datetime(end_date)
            cta_filtered = cta_filtered[cta_filtered['trade_date'] <= end_date]
            position_filtered = position_filtered[position_filtered['trade_date'] <= end_date]
        
        logger.info(f"数据过滤完成，CTA数据: {len(cta_filtered)} 条，持仓数据: {len(position_filtered)} 条")
        
        return cta_filtered, position_filtered
