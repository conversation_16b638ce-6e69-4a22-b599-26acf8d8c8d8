"""
每日盈亏序列数据模块
专门负责生成每日透视表和序列数据
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

def generate_daily_profit_loss_pivot(cta_data_df: pd.DataFrame, analysis_config: dict, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> Dict[str, pd.DataFrame]:
    """
    生成指定类别和字段的每日透视表。

    Args:
        cta_data_df (pd.DataFrame): 预处理后的CTA每日盈亏数据。
        analysis_config (dict): 来自analysis_config.json的盈亏分析配置。
        start_date (datetime, optional): 分析期间的开始日期。默认为None。
        end_date (datetime, optional): 分析期间的结束日期。默认为None。

    Returns:
        dict: 字典，键为描述性名称（如"strategy_category_daily_pivot"），
              值为对应的每日序列Pandas DataFrames。
    """
    # 时间筛选
    filtered_df = cta_data_df.copy()
    if start_date:
        filtered_df = filtered_df[filtered_df['trade_date'] >= start_date]
    if end_date:
        filtered_df = filtered_df[filtered_df['trade_date'] <= end_date]

    if filtered_df.empty:
        logger.warning(f"期间 {start_date} 到 {end_date} 无每日盈亏数据可供分析。")
        return {}

    daily_results = {}
    
    # 获取配置
    daily_series_config = analysis_config.get('daily_profit_loss_series_config', {})
    aggregation_method = analysis_config.get('profit_loss_analysis', {}).get('aggregation_method', 'sum')

    # 1. 根据"strategy_category"等字段汇总profit_loss_amount（求和）
    daily_pivot_fields_level_1 = daily_series_config.get('daily_pivot_fields_level_1', [])
    for field in daily_pivot_fields_level_1:
        if field in filtered_df.columns:
            # 创建每日透视表
            daily_pivot = filtered_df.groupby(['trade_date', field])['profit_loss_amount'].agg(aggregation_method).unstack(fill_value=0)
            daily_pivot['total'] = daily_pivot.sum(axis=1)  # 添加总计列
            daily_pivot_reset = daily_pivot.reset_index()
            daily_results[f"{field}_daily_pivot"] = daily_pivot_reset
            logger.info(f"完成 {field} 每日透视表生成")
        else:
            logger.warning(f"字段 '{field}' 在数据中未找到，跳过每日透视。")

    # 2. 筛选"strategy_category"等字段，根据"position_direction"等汇总每日盈亏
    filter_fields_2_primary = daily_series_config.get('filter_fields_level_2_primary', [])
    pivot_fields_2_secondary = daily_series_config.get('pivot_fields_level_2_secondary', [])

    for primary_field in filter_fields_2_primary:
        if primary_field not in filtered_df.columns:
            continue
        
        for primary_value in filtered_df[primary_field].unique():
            subset_df = filtered_df[filtered_df[primary_field] == primary_value].copy()
            if subset_df.empty:
                continue
            
            for secondary_field in pivot_fields_2_secondary:
                if secondary_field in subset_df.columns:
                    # 创建子集每日透视表
                    daily_pivot_sub = subset_df.groupby(['trade_date', secondary_field])['profit_loss_amount'].agg(aggregation_method).unstack(fill_value=0)
                    daily_pivot_sub['total'] = daily_pivot_sub.sum(axis=1)  # 添加总计列
                    daily_pivot_sub_reset = daily_pivot_sub.reset_index()
                    daily_results[f"{primary_field}_{primary_value}_{secondary_field}_daily_pivot"] = daily_pivot_sub_reset
                    logger.info(f"完成 {primary_field}_{primary_value}_{secondary_field} 每日透视表生成")
                else:
                    logger.warning(f"次级字段 '{secondary_field}' 在子集中未找到，跳过每日透视。")

    logger.info(f"每日盈亏序列数据生成完成，共生成 {len(daily_results)} 个透视表")
    return daily_results

class DailyProfitLossAnalyzer:
    """每日盈亏序列分析器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化每日盈亏序列分析器
        
        Args:
            config: 分析配置字典
        """
        self.config = config
        self.daily_series_config = config.get('daily_profit_loss_series_config', {})
        self.pnl_config = config.get('profit_loss_analysis', {})
        self.aggregation_method = self.pnl_config.get('aggregation_method', 'sum')
        
    def generate_daily_series(self, cta_data: pd.DataFrame, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> Dict[str, pd.DataFrame]:
        """
        生成每日序列数据
        
        Args:
            cta_data: CTA策略数据
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            每日序列数据字典
        """
        return generate_daily_profit_loss_pivot(cta_data, self.config, start_date, end_date)
    
    def generate_comprehensive_daily_pivot(self, cta_data: pd.DataFrame, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> Dict[str, pd.DataFrame]:
        """
        生成综合每日透视数据（包含所有维度）
        
        Args:
            cta_data: CTA策略数据
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            综合每日透视数据字典
        """
        try:
            # 时间筛选
            filtered_df = cta_data.copy()
            if start_date:
                filtered_df = filtered_df[filtered_df['trade_date'] >= start_date]
            if end_date:
                filtered_df = filtered_df[filtered_df['trade_date'] <= end_date]
            
            if filtered_df.empty:
                logger.warning(f"期间 {start_date} 到 {end_date} 无数据可供分析。")
                return {}
            
            comprehensive_results = {}
            
            # 基础维度透视
            basic_dimensions = ['strategy_category', 'strategy_signal', 'signal_freq', 'position_direction', 'symbol_category', 'industry']
            
            for dimension in basic_dimensions:
                if dimension in filtered_df.columns:
                    daily_pivot = filtered_df.pivot_table(
                        index='trade_date',
                        columns=dimension,
                        values='profit_loss_amount',
                        aggfunc=self.aggregation_method,
                        fill_value=0
                    )
                    
                    # 添加总计列
                    daily_pivot['all'] = daily_pivot.sum(axis=1)
                    daily_pivot_reset = daily_pivot.reset_index()
                    comprehensive_results[f"{dimension}_comprehensive_daily"] = daily_pivot_reset
            
            logger.info(f"综合每日透视数据生成完成，共 {len(comprehensive_results)} 个维度")
            return comprehensive_results
            
        except Exception as e:
            logger.error(f"生成综合每日透视数据失败: {str(e)}")
            raise
    
    def save_daily_series_to_excel(self, daily_series_data: Dict[str, pd.DataFrame], output_path: str):
        """
        将每日序列数据保存到Excel文件
        
        Args:
            daily_series_data: 每日序列数据字典
            output_path: 输出文件路径
        """
        try:
            if not daily_series_data:
                logger.warning("没有每日序列数据可保存")
                return
            
            with pd.ExcelWriter(output_path, engine='openpyxl', mode='w') as writer:
                for sheet_name, df in daily_series_data.items():
                    # 限制工作表名称长度
                    safe_sheet_name = sheet_name[:31] if len(sheet_name) > 31 else sheet_name
                    df.to_excel(writer, sheet_name=safe_sheet_name, index=False)
                    logger.info(f"已保存工作表: {safe_sheet_name}")
            
            logger.info(f"每日序列数据已保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"保存每日序列数据到Excel失败: {str(e)}")
            raise
