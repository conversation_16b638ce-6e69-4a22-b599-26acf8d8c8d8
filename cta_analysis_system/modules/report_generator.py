"""
报告生成模块
负责整合各分析模块结果并生成综合报告
"""

import pandas as pd
import numpy as np
import logging
import os
import json
from datetime import datetime, date
from typing import Dict, List, Any, Tuple
import base64

logger = logging.getLogger(__name__)

class ReportGenerator:
    """报告生成器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化报告生成器
        
        Args:
            config: 报告配置字典
        """
        self.config = config.get('report_settings', {})
        self.output_settings = config.get('output_settings', {})
        
    def generate_reports(self, analysis_results: Dict[str, Any], report_type: str = 'all') -> Dict[str, str]:
        """
        生成综合分析报告
        
        Args:
            analysis_results: 各模块分析结果
            report_type: 报告类型 ('today', 'this_year', 'custom_period', 'all')
            
        Returns:
            生成的报告文件路径字典
        """
        report_paths = {}
        
        try:
            # 根据报告类型确定要生成的报告
            reports_to_generate = []
            
            if report_type == 'all':
                if self.config.get('today_report', {}).get('enabled', False):
                    reports_to_generate.append('today_report')
                if self.config.get('this_year_report', {}).get('enabled', False):
                    reports_to_generate.append('this_year_report')
                if self.config.get('custom_period_report', {}).get('enabled', False):
                    reports_to_generate.append('custom_period_report')
            else:
                # 映射报告类型到配置键
                report_type_mapping = {
                    'today': 'today_report',
                    'this_year': 'this_year_report',
                    'custom_period': 'custom_period_report'
                }
                config_key = report_type_mapping.get(report_type, f'{report_type}_report')
                reports_to_generate.append(config_key)
            
            # 生成各类型报告
            for report_config_key in reports_to_generate:
                report_config = self.config.get(report_config_key, {})
                if not report_config.get('enabled', False):
                    continue
                
                # 确定报告名称和时间范围
                report_name, time_range = self._get_report_info(report_config_key, report_config)
                
                # 过滤分析结果
                filtered_results = self._filter_results_by_modules(
                    analysis_results, report_config.get('included_modules', [])
                )
                
                # 生成不同格式的报告
                output_formats = report_config.get('output_formats', ['markdown'])
                
                for output_format in output_formats:
                    if output_format == 'markdown':
                        report_path = self._generate_markdown_report(
                            filtered_results, report_name, time_range, report_config_key
                        )
                        report_paths[f'{report_name}_markdown'] = report_path
                    
                    elif output_format == 'html':
                        report_path = self._generate_html_report(
                            filtered_results, report_name, time_range, report_config_key
                        )
                        report_paths[f'{report_name}_html'] = report_path
                    
                    elif output_format == 'excel':
                        report_path = self._generate_excel_report(
                            filtered_results, report_name, time_range, report_config_key
                        )
                        report_paths[f'{report_name}_excel'] = report_path
            
            logger.info(f"报告生成完成，共生成 {len(report_paths)} 个报告文件")
            return report_paths
            
        except Exception as e:
            logger.error(f"报告生成失败: {str(e)}")
            raise
    
    def _get_report_info(self, report_type: str, report_config: Dict[str, Any]) -> Tuple[str, str]:
        """
        获取报告信息
        
        Args:
            report_type: 报告类型
            report_config: 报告配置
            
        Returns:
            (报告名称, 时间范围描述)
        """
        today = datetime.now()
        
        if report_type == 'today_report':
            report_name = f"每日报告_{today.strftime('%Y%m%d')}"
            time_range = today.strftime('%Y年%m月%d日')
        
        elif report_type == 'this_year_report':
            year = report_config.get('year', today.year)
            report_name = f"年度报告_{year}"
            time_range = f"{year}年度"
        
        elif report_type == 'custom_period_report':
            start_date = report_config.get('start_date', '2025-01-01')
            end_date = report_config.get('end_date', today.strftime('%Y-%m-%d'))
            report_name = f"自定义期间报告_{start_date}_{end_date}"
            time_range = f"{start_date} 至 {end_date}"
        
        else:
            report_name = f"综合报告_{today.strftime('%Y%m%d')}"
            time_range = "全期间"
        
        return report_name, time_range
    
    def _filter_results_by_modules(self, analysis_results: Dict[str, Any], 
                                 included_modules: List[str]) -> Dict[str, Any]:
        """
        根据包含的模块过滤分析结果
        
        Args:
            analysis_results: 完整分析结果
            included_modules: 要包含的模块列表
            
        Returns:
            过滤后的分析结果
        """
        filtered_results = {}
        
        for module in included_modules:
            if module in analysis_results:
                filtered_results[module] = analysis_results[module]
            else:
                logger.warning(f"分析结果中缺少模块: {module}")
        
        return filtered_results
    
    def _generate_markdown_report(self, analysis_results: Dict[str, Any], 
                                report_name: str, time_range: str, 
                                report_type: str) -> str:
        """
        生成Markdown格式报告
        
        Args:
            analysis_results: 分析结果
            report_name: 报告名称
            time_range: 时间范围
            report_type: 报告类型
            
        Returns:
            报告文件路径
        """
        # 确定输出目录
        if report_type == 'today_report':
            output_dir = "reports/daily"
        elif report_type == 'this_year_report':
            output_dir = "reports/yearly"
        else:
            output_dir = "reports/custom_period"
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成报告内容
        report_lines = []
        
        # 报告标题和基本信息
        report_lines.append(f"# {report_name}")
        report_lines.append("")
        report_lines.append(f"**分析时间范围**: {time_range}")
        report_lines.append(f"**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        report_lines.append("---")
        report_lines.append("")
        
        # 目录
        if self.output_settings.get('markdown', {}).get('include_toc', True):
            report_lines.append("## 目录")
            report_lines.append("")
            toc_items = []
            if 'profit_loss_analysis' in analysis_results:
                toc_items.append("- [盈亏分析](#盈亏分析)")
            if 'position_analysis' in analysis_results:
                toc_items.append("- [持仓分析](#持仓分析)")
            if 'returns_calculation' in analysis_results:
                toc_items.append("- [收益率分析](#收益率分析)")
            if 'performance_risk_analysis' in analysis_results:
                toc_items.append("- [性能风险分析](#性能风险分析)")
            if 'contribution_analysis' in analysis_results:
                toc_items.append("- [贡献分析](#贡献分析)")
            
            report_lines.extend(toc_items)
            report_lines.append("")
            report_lines.append("---")
            report_lines.append("")
        
        # 各模块分析结果
        if 'profit_loss_analysis' in analysis_results:
            report_lines.extend(self._format_profit_loss_section(analysis_results['profit_loss_analysis']))
        
        if 'position_analysis' in analysis_results:
            report_lines.extend(self._format_position_section(analysis_results['position_analysis']))
        
        if 'returns_calculation' in analysis_results:
            report_lines.extend(self._format_returns_section(analysis_results['returns_calculation']))
        
        if 'performance_risk_analysis' in analysis_results:
            report_lines.extend(self._format_performance_risk_section(analysis_results['performance_risk_analysis']))
        
        if 'contribution_analysis' in analysis_results:
            report_lines.extend(self._format_contribution_section(analysis_results['contribution_analysis']))
        
        # 保存报告
        report_content = "\n".join(report_lines)
        report_file = os.path.join(output_dir, f"{report_name}.md")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"Markdown报告已生成: {report_file}")
        return report_file
    
    def _format_profit_loss_section(self, pnl_results: Dict[str, Any]) -> List[str]:
        """格式化盈亏分析部分"""
        lines = []
        lines.append("## 盈亏分析")
        lines.append("")
        
        # 总体统计
        if 'overall_statistics' in pnl_results:
            stats = pnl_results['overall_statistics']
            lines.append("### 总体统计")
            lines.append("")
            lines.append("| 指标 | 数值 |")
            lines.append("|------|------|")
            
            for key, value in stats.items():
                if not isinstance(value, (dict, pd.DataFrame)):
                    if isinstance(value, (int, float)):
                        if '率' in key or '比' in key:
                            formatted_value = f"{value:.2%}" if abs(value) <= 1 else f"{value:.2f}"
                        else:
                            formatted_value = f"{value:,.2f}"
                    else:
                        formatted_value = str(value)
                    lines.append(f"| {key} | {formatted_value} |")
            
            lines.append("")
        
        # 多维度分析摘要
        if 'multi_dimension_analysis' in pnl_results:
            lines.append("### 多维度分析摘要")
            lines.append("")
            multi_dim = pnl_results['multi_dimension_analysis']
            
            for category, results in multi_dim.items():
                lines.append(f"#### {category}")
                lines.append("")
                lines.append(f"- 分析维度数量: {len(results)}")
                for subcategory in results.keys():
                    lines.append(f"  - {subcategory}")
                lines.append("")
        
        lines.append("---")
        lines.append("")
        return lines

    def _format_position_section(self, position_results: Dict[str, Any]) -> List[str]:
        """格式化持仓分析部分"""
        lines = []
        lines.append("## 持仓分析")
        lines.append("")

        # 持仓分布统计
        if 'position_distribution' in position_results:
            lines.append("### 持仓分布统计")
            lines.append("")
            dist_stats = position_results['position_distribution']

            lines.append("| 指标 | 数值 |")
            lines.append("|------|------|")

            for key, value in dist_stats.items():
                if not isinstance(value, (dict, pd.DataFrame)):
                    formatted_value = f"{value:,.2f}" if isinstance(value, (int, float)) else str(value)
                    lines.append(f"| {key} | {formatted_value} |")

            lines.append("")

        # 集中度分析摘要
        if 'concentration_analysis' in position_results:
            lines.append("### 集中度分析摘要")
            lines.append("")
            conc_analysis = position_results['concentration_analysis']

            for analysis_type, conc_data in conc_analysis.items():
                if isinstance(conc_data, pd.DataFrame) and not conc_data.empty:
                    lines.append(f"#### {analysis_type}")
                    lines.append("")

                    # 统计各集中度等级的数量
                    if '集中度等级' in conc_data.columns:
                        level_counts = conc_data['集中度等级'].value_counts()
                        for level, count in level_counts.items():
                            lines.append(f"- {level}集中度: {count} 项")
                    lines.append("")

        lines.append("---")
        lines.append("")
        return lines

    def _format_returns_section(self, returns_results: Dict[str, Any]) -> List[str]:
        """格式化收益率分析部分"""
        lines = []
        lines.append("## 收益率分析")
        lines.append("")

        if 'returns_summary' in returns_results:
            lines.append("### 收益率汇总")
            lines.append("")

            summary_df = returns_results['returns_summary']
            if isinstance(summary_df, pd.DataFrame) and not summary_df.empty:
                # 转换为Markdown表格
                lines.append("| " + " | ".join(summary_df.columns) + " |")
                lines.append("|" + "------|" * len(summary_df.columns))

                for _, row in summary_df.iterrows():
                    row_values = [str(val) for val in row.values]
                    lines.append("| " + " | ".join(row_values) + " |")

                lines.append("")

        # 各策略收益率详情
        for target, result in returns_results.items():
            if target == 'returns_summary':
                continue

            if isinstance(result, dict) and 'description' in result:
                lines.append(f"### {result['description']}")
                lines.append("")

                if 'performance_metrics' in result:
                    metrics = result['performance_metrics']
                    lines.append("| 指标 | 数值 |")
                    lines.append("|------|------|")

                    for metric, value in metrics.items():
                        if isinstance(value, (int, float)):
                            if '率' in metric or '比' in metric:
                                formatted_value = f"{value:.2%}" if abs(value) <= 1 else f"{value:.2f}"
                            else:
                                formatted_value = f"{value:.4f}"
                        else:
                            formatted_value = str(value)
                        lines.append(f"| {metric} | {formatted_value} |")

                    lines.append("")

        lines.append("---")
        lines.append("")
        return lines

    def _format_performance_risk_section(self, perf_risk_results: Dict[str, Any]) -> List[str]:
        """格式化性能风险分析部分"""
        lines = []
        lines.append("## 性能风险分析")
        lines.append("")

        for target, analysis in perf_risk_results.items():
            if not isinstance(analysis, dict):
                continue

            lines.append(f"### {target}")
            lines.append("")

            # 性能指标
            if 'performance_metrics' in analysis:
                lines.append("#### 性能指标")
                lines.append("")
                lines.append("| 指标 | 数值 |")
                lines.append("|------|------|")

                metrics = analysis['performance_metrics']
                for metric, value in metrics.items():
                    if isinstance(value, (int, float)):
                        if '率' in metric or '比' in metric:
                            formatted_value = f"{value:.2%}" if abs(value) <= 1 else f"{value:.2f}"
                        else:
                            formatted_value = f"{value:.4f}"
                    else:
                        formatted_value = str(value)
                    lines.append(f"| {metric} | {formatted_value} |")

                lines.append("")

            # 风险指标
            if 'risk_metrics' in analysis:
                lines.append("#### 风险指标")
                lines.append("")

                risk_metrics = analysis['risk_metrics']

                # VaR指标
                if 'VaR' in risk_metrics:
                    lines.append("**VaR指标:**")
                    lines.append("")
                    for var_type, var_value in risk_metrics['VaR'].items():
                        lines.append(f"- {var_type}: {var_value:.4f}")
                    lines.append("")

                # ES指标
                if 'ES' in risk_metrics:
                    lines.append("**ES指标:**")
                    lines.append("")
                    for es_type, es_value in risk_metrics['ES'].items():
                        lines.append(f"- {es_type}: {es_value:.4f}")
                    lines.append("")

            # 回撤分析
            if 'drawdown_analysis' in analysis:
                lines.append("#### 回撤分析")
                lines.append("")

                dd_analysis = analysis['drawdown_analysis']
                lines.append("| 指标 | 数值 |")
                lines.append("|------|------|")

                for metric, value in dd_analysis.items():
                    if not isinstance(value, dict):
                        if isinstance(value, (int, float)):
                            formatted_value = f"{value:.4f}"
                        else:
                            formatted_value = str(value)
                        lines.append(f"| {metric} | {formatted_value} |")

                lines.append("")

        lines.append("---")
        lines.append("")
        return lines

    def _format_contribution_section(self, contribution_results: Dict[str, Any]) -> List[str]:
        """格式化贡献分析部分"""
        lines = []
        lines.append("## 贡献分析")
        lines.append("")

        # 贡献分析摘要
        if 'contribution_summary' in contribution_results:
            lines.append("### 贡献分析摘要")
            lines.append("")

            summary = contribution_results['contribution_summary']
            for analysis_type, field_summary in summary.items():
                if not isinstance(field_summary, dict):
                    continue

                field_desc = field_summary.get('字段描述', analysis_type)
                lines.append(f"#### {field_desc}")
                lines.append("")

                # 最大贡献者
                max_profit = field_summary.get('最大盈利贡献者')
                max_loss = field_summary.get('最大亏损贡献者')

                if max_profit:
                    lines.append(f"- **最大盈利贡献者**: {max_profit['类别']} ({max_profit['贡献率']:.2%})")

                if max_loss:
                    lines.append(f"- **最大亏损贡献者**: {max_loss['类别']} ({max_loss['贡献率']:.2%})")

                # 贡献集中度
                concentration = field_summary.get('贡献集中度', {})
                if concentration:
                    lines.append(f"- **前3盈利贡献率**: {concentration.get('前3盈利贡献率', 0):.2%}")
                    lines.append(f"- **前3亏损贡献率**: {concentration.get('前3亏损贡献率', 0):.2%}")

                lines.append("")

        lines.append("---")
        lines.append("")
        return lines

    def _generate_html_report(self, analysis_results: Dict[str, Any],
                            report_name: str, time_range: str,
                            report_type: str) -> str:
        """
        生成HTML格式报告

        Args:
            analysis_results: 分析结果
            report_name: 报告名称
            time_range: 时间范围
            report_type: 报告类型

        Returns:
            报告文件路径
        """
        # 确定输出目录
        if report_type == 'today_report':
            output_dir = "reports/daily"
        elif report_type == 'this_year_report':
            output_dir = "reports/yearly"
        else:
            output_dir = "reports/custom_period"

        os.makedirs(output_dir, exist_ok=True)

        # 生成HTML内容
        html_content = self._create_html_template(report_name, time_range, analysis_results)

        # 保存HTML报告
        report_file = os.path.join(output_dir, f"{report_name}.html")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"HTML报告已生成: {report_file}")
        return report_file

    def _create_html_template(self, report_name: str, time_range: str,
                            analysis_results: Dict[str, Any]) -> str:
        """创建HTML报告模板"""

        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{report_name}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }}
        h3 {{
            color: #7f8c8d;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #3498db;
            color: white;
        }}
        tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        .metric-positive {{
            color: #27ae60;
            font-weight: bold;
        }}
        .metric-negative {{
            color: #e74c3c;
            font-weight: bold;
        }}
        .section {{
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ecf0f1;
            border-radius: 5px;
        }}
        .info-box {{
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{report_name}</h1>

        <div class="info-box">
            <strong>分析时间范围:</strong> {time_range}<br>
            <strong>报告生成时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        </div>

        {self._generate_html_sections(analysis_results)}

        <div style="margin-top: 50px; text-align: center; color: #7f8c8d;">
            <p>报告由CTA策略分析系统自动生成</p>
        </div>
    </div>
</body>
</html>
        """

        return html_template

    def _generate_html_sections(self, analysis_results: Dict[str, Any]) -> str:
        """生成HTML报告各部分内容"""
        sections = []

        # 盈亏分析部分
        if 'profit_loss_analysis' in analysis_results:
            sections.append(self._create_html_pnl_section(analysis_results['profit_loss_analysis']))

        # 持仓分析部分
        if 'position_analysis' in analysis_results:
            sections.append(self._create_html_position_section(analysis_results['position_analysis']))

        # 收益率分析部分
        if 'returns_calculation' in analysis_results:
            sections.append(self._create_html_returns_section(analysis_results['returns_calculation']))

        # 性能风险分析部分
        if 'performance_risk_analysis' in analysis_results:
            sections.append(self._create_html_performance_section(analysis_results['performance_risk_analysis']))

        # 贡献分析部分
        if 'contribution_analysis' in analysis_results:
            sections.append(self._create_html_contribution_section(analysis_results['contribution_analysis']))

        return '\n'.join(sections)

    def _create_html_pnl_section(self, pnl_results: Dict[str, Any]) -> str:
        """创建HTML盈亏分析部分"""
        html = '<div class="section"><h2>盈亏分析</h2>'

        if 'overall_statistics' in pnl_results:
            stats = pnl_results['overall_statistics']
            html += '<h3>总体统计</h3><table><tr><th>指标</th><th>数值</th></tr>'

            for key, value in stats.items():
                if not isinstance(value, (dict, pd.DataFrame)):
                    if isinstance(value, (int, float)):
                        if '率' in key or '比' in key:
                            formatted_value = f"{value:.2%}" if abs(value) <= 1 else f"{value:.2f}"
                        else:
                            formatted_value = f"{value:,.2f}"

                        # 添加颜色样式
                        if value > 0 and ('盈' in key or '收益' in key):
                            css_class = 'metric-positive'
                        elif value < 0 and ('亏' in key or '损' in key):
                            css_class = 'metric-negative'
                        else:
                            css_class = ''

                        html += f'<tr><td>{key}</td><td class="{css_class}">{formatted_value}</td></tr>'
                    else:
                        html += f'<tr><td>{key}</td><td>{str(value)}</td></tr>'

            html += '</table>'

        html += '</div>'
        return html

    def _create_html_position_section(self, position_results: Dict[str, Any]) -> str:
        """创建HTML持仓分析部分"""
        return '<div class="section"><h2>持仓分析</h2><p>持仓分析详情...</p></div>'

    def _create_html_returns_section(self, returns_results: Dict[str, Any]) -> str:
        """创建HTML收益率分析部分"""
        return '<div class="section"><h2>收益率分析</h2><p>收益率分析详情...</p></div>'

    def _create_html_performance_section(self, perf_results: Dict[str, Any]) -> str:
        """创建HTML性能风险分析部分"""
        return '<div class="section"><h2>性能风险分析</h2><p>性能风险分析详情...</p></div>'

    def _create_html_contribution_section(self, contrib_results: Dict[str, Any]) -> str:
        """创建HTML贡献分析部分"""
        return '<div class="section"><h2>贡献分析</h2><p>贡献分析详情...</p></div>'

    def _generate_excel_report(self, analysis_results: Dict[str, Any],
                             report_name: str, time_range: str,
                             report_type: str) -> str:
        """
        生成Excel格式报告

        Args:
            analysis_results: 分析结果
            report_name: 报告名称
            time_range: 时间范围
            report_type: 报告类型

        Returns:
            报告文件路径
        """
        try:
            # 确定输出目录
            if report_type == 'today_report':
                output_dir = "reports/daily"
            elif report_type == 'this_year_report':
                output_dir = "reports/yearly"
            else:
                output_dir = "reports/custom_period"

            os.makedirs(output_dir, exist_ok=True)

            # 创建Excel文件
            report_file = os.path.join(output_dir, f"{report_name}.xlsx")

            with pd.ExcelWriter(report_file, engine='openpyxl') as writer:
                # 创建概览工作表
                self._create_overview_sheet(writer, report_name, time_range, analysis_results)

                # 创建各分析模块工作表
                if 'profit_loss_analysis' in analysis_results:
                    self._create_pnl_excel_sheets(writer, analysis_results['profit_loss_analysis'])

                if 'position_analysis' in analysis_results:
                    self._create_position_excel_sheets(writer, analysis_results['position_analysis'])

                if 'returns_calculation' in analysis_results:
                    self._create_returns_excel_sheets(writer, analysis_results['returns_calculation'])

                if 'performance_risk_analysis' in analysis_results:
                    self._create_performance_excel_sheets(writer, analysis_results['performance_risk_analysis'])

                if 'contribution_analysis' in analysis_results:
                    self._create_contribution_excel_sheets(writer, analysis_results['contribution_analysis'])

            logger.info(f"Excel报告已生成: {report_file}")
            return report_file

        except Exception as e:
            logger.error(f"生成Excel报告失败: {str(e)}")
            raise

    def _create_overview_sheet(self, writer: pd.ExcelWriter, report_name: str,
                             time_range: str, analysis_results: Dict[str, Any]):
        """创建Excel概览工作表"""
        overview_data = []

        # 基本信息
        overview_data.append(['报告名称', report_name])
        overview_data.append(['分析时间范围', time_range])
        overview_data.append(['报告生成时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
        overview_data.append(['', ''])  # 空行

        # 包含的分析模块
        overview_data.append(['包含的分析模块', ''])
        for module_name in analysis_results.keys():
            module_desc = {
                'profit_loss_analysis': '盈亏分析',
                'position_analysis': '持仓分析',
                'returns_calculation': '收益率计算',
                'performance_risk_analysis': '性能风险分析',
                'contribution_analysis': '贡献分析'
            }.get(module_name, module_name)
            overview_data.append(['', module_desc])

        overview_df = pd.DataFrame(overview_data, columns=['项目', '内容'])
        overview_df.to_excel(writer, sheet_name='概览', index=False)

    def _create_pnl_excel_sheets(self, writer: pd.ExcelWriter, pnl_results: Dict[str, Any]):
        """创建盈亏分析Excel工作表"""
        # 总体统计工作表
        if 'overall_statistics' in pnl_results:
            stats = pnl_results['overall_statistics']
            stats_data = []

            for key, value in stats.items():
                if not isinstance(value, (dict, pd.DataFrame)):
                    stats_data.append([key, value])

            if stats_data:
                stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
                stats_df.to_excel(writer, sheet_name='盈亏总体统计', index=False)

        # 时间维度分析工作表
        if 'time_dimension_analysis' in pnl_results:
            time_analysis = pnl_results['time_dimension_analysis']

            if 'daily_summary' in time_analysis:
                daily_df = time_analysis['daily_summary']
                if isinstance(daily_df, pd.DataFrame) and not daily_df.empty:
                    daily_df.to_excel(writer, sheet_name='每日盈亏汇总', index=False)

            if 'monthly_summary' in time_analysis:
                monthly_df = time_analysis['monthly_summary']
                if isinstance(monthly_df, pd.DataFrame) and not monthly_df.empty:
                    monthly_df.to_excel(writer, sheet_name='月度盈亏汇总', index=False)

    def _create_position_excel_sheets(self, writer: pd.ExcelWriter, position_results: Dict[str, Any]):
        """创建持仓分析Excel工作表"""
        # 集中度分析
        if 'concentration_analysis' in position_results:
            conc_analysis = position_results['concentration_analysis']

            for analysis_type, conc_data in conc_analysis.items():
                if isinstance(conc_data, pd.DataFrame) and not conc_data.empty:
                    sheet_name = f'集中度_{analysis_type}'[:31]  # Excel工作表名称限制
                    conc_data.to_excel(writer, sheet_name=sheet_name, index=False)

    def _create_returns_excel_sheets(self, writer: pd.ExcelWriter, returns_results: Dict[str, Any]):
        """创建收益率分析Excel工作表"""
        if 'returns_summary' in returns_results:
            summary_df = returns_results['returns_summary']
            if isinstance(summary_df, pd.DataFrame) and not summary_df.empty:
                summary_df.to_excel(writer, sheet_name='收益率汇总', index=False)

    def _create_performance_excel_sheets(self, writer: pd.ExcelWriter, perf_results: Dict[str, Any]):
        """创建性能风险分析Excel工作表"""
        # 为每个策略创建工作表
        for target, analysis in perf_results.items():
            if not isinstance(analysis, dict):
                continue

            # 性能指标
            if 'performance_metrics' in analysis:
                metrics_data = []
                for metric, value in analysis['performance_metrics'].items():
                    metrics_data.append([metric, value])

                if metrics_data:
                    metrics_df = pd.DataFrame(metrics_data, columns=['指标', '数值'])
                    sheet_name = f'性能_{target}'[:31]
                    metrics_df.to_excel(writer, sheet_name=sheet_name, index=False)

    def _create_contribution_excel_sheets(self, writer: pd.ExcelWriter, contrib_results: Dict[str, Any]):
        """创建贡献分析Excel工作表"""
        for analysis_type, result in contrib_results.items():
            if not isinstance(result, dict) or 'contribution_data' not in result:
                continue

            contribution_data = result['contribution_data']
            if isinstance(contribution_data, pd.DataFrame) and not contribution_data.empty:
                sheet_name = f'贡献_{analysis_type}'[:31]
                contribution_data.to_excel(writer, sheet_name=sheet_name, index=False)
